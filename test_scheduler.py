#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
定时任务框架测试脚本
用于验证调度器功能是否正常

Author: 外包程序员
Date: 2025-07-29
"""

import time
import json
from datetime import datetime
from scheduler import SimpleScheduler, BaseTask, TaskStatus


class TestTask(BaseTask):
    """测试任务类"""
    
    def __init__(self, task_id: str, message: str = "Hello"):
        super().__init__(
            task_id=task_id,
            name=f"测试任务-{task_id}",
            description=f"测试任务，输出消息: {message}"
        )
        self.message = message
        self.execution_count = 0
    
    def execute(self):
        """执行测试任务"""
        self.execution_count += 1
        current_time = datetime.now().strftime("%H:%M:%S")
        
        result = {
            'message': self.message,
            'execution_count': self.execution_count,
            'execution_time': current_time,
            'task_id': self.task_id
        }
        
        print(f"🔄 [{current_time}] 执行任务 {self.task_id}: {self.message} (第{self.execution_count}次)")
        
        return result
    
    def on_success(self, result):
        """成功回调"""
        print(f"✅ 任务 {self.task_id} 执行成功")
    
    def on_error(self, error):
        """失败回调"""
        print(f"❌ 任务 {self.task_id} 执行失败: {error}")


class FailingTask(BaseTask):
    """故意失败的测试任务"""
    
    def __init__(self, task_id: str):
        super().__init__(
            task_id=task_id,
            name="失败测试任务",
            description="用于测试错误处理的任务"
        )
    
    def execute(self):
        """故意抛出异常"""
        raise Exception("这是一个测试异常")
    
    def on_error(self, error):
        """失败回调"""
        print(f"🔴 预期的失败任务: {self.task_id} - {error}")


def test_basic_functionality():
    """测试基本功能"""
    print("🧪 测试1: 基本功能测试")
    print("=" * 40)
    
    # 创建调度器
    scheduler = SimpleScheduler("test_scheduler_config.json")
    
    # 添加测试任务
    task1 = TestTask("test_1", "每分钟任务")
    task2 = TestTask("test_2", "每2分钟任务")
    task3 = FailingTask("fail_test")
    
    # 添加到调度器
    scheduler.add_task(task1, "* * * * *")      # 每分钟
    scheduler.add_task(task2, "*/2 * * * *")    # 每2分钟
    scheduler.add_task(task3, "*/3 * * * *")    # 每3分钟（失败任务）
    
    # 显示任务列表
    tasks = scheduler.get_all_tasks()
    print(f"📋 已添加 {len(tasks)} 个任务:")
    for task in tasks:
        print(f"   - {task['name']} ({task['task_id']}) - {task['cron_expr']}")
    
    print("\n🚀 启动调度器...")
    scheduler.start()
    
    # 运行5分钟进行测试
    print("⏰ 运行5分钟进行测试...")
    try:
        for i in range(300):  # 5分钟 = 300秒
            time.sleep(1)
            
            # 每30秒显示一次状态
            if i % 30 == 0 and i > 0:
                print(f"\n📊 运行状态 (已运行{i//60}分{i%60}秒):")
                for task_id in ["test_1", "test_2", "fail_test"]:
                    result = scheduler.get_task_status(task_id)
                    if result:
                        print(f"   {task_id}: {result.status.value} - 最后执行: {result.start_time.strftime('%H:%M:%S')}")
                    else:
                        print(f"   {task_id}: 未执行")
                print()
    
    except KeyboardInterrupt:
        print("\n⏹️ 手动停止测试")
    
    print("\n🛑 停止调度器...")
    scheduler.stop()
    
    # 显示最终结果
    print("\n📈 测试结果:")
    for task_id in ["test_1", "test_2", "fail_test"]:
        result = scheduler.get_task_status(task_id)
        if result:
            print(f"   {task_id}:")
            print(f"     状态: {result.status.value}")
            print(f"     开始时间: {result.start_time}")
            print(f"     结束时间: {result.end_time}")
            if result.error:
                print(f"     错误: {result.error}")
            if result.result:
                print(f"     结果: {result.result}")
    
    print("✅ 基本功能测试完成\n")


def test_cron_parser():
    """测试Cron解析器"""
    print("🧪 测试2: Cron表达式解析测试")
    print("=" * 40)
    
    from scheduler import CronParser
    
    test_cases = [
        "* * * * *",        # 每分钟
        "0 * * * *",        # 每小时
        "0 9 * * *",        # 每天9点
        "*/5 * * * *",      # 每5分钟
        "0 9 * * 1-5",      # 工作日9点
        "0 2 1 * *",        # 每月1号2点
    ]
    
    for cron_expr in test_cases:
        try:
            result = CronParser.parse_cron(cron_expr)
            print(f"✅ {cron_expr:12} -> {result}")
        except Exception as e:
            print(f"❌ {cron_expr:12} -> 错误: {e}")
    
    print("✅ Cron解析测试完成\n")


def test_config_persistence():
    """测试配置持久化"""
    print("🧪 测试3: 配置持久化测试")
    print("=" * 40)
    
    config_file = "test_persistence_config.json"
    
    # 创建调度器并添加任务
    scheduler1 = SimpleScheduler(config_file)
    task = TestTask("persist_test", "持久化测试")
    scheduler1.add_task(task, "*/10 * * * *")
    
    print("📝 已创建任务并保存配置")
    
    # 创建新的调度器实例，应该能加载之前的配置
    scheduler2 = SimpleScheduler(config_file)
    tasks = scheduler2.get_all_tasks()
    
    if len(tasks) > 0 and tasks[0]['task_id'] == 'persist_test':
        print("✅ 配置持久化测试成功")
    else:
        print("❌ 配置持久化测试失败")
    
    # 清理测试文件
    import os
    try:
        os.remove(config_file)
        print("🧹 已清理测试配置文件")
    except:
        pass
    
    print("✅ 配置持久化测试完成\n")


def test_task_management():
    """测试任务管理功能"""
    print("🧪 测试4: 任务管理功能测试")
    print("=" * 40)
    
    scheduler = SimpleScheduler("test_management_config.json")
    
    # 添加任务
    task1 = TestTask("mgmt_1", "管理测试1")
    task2 = TestTask("mgmt_2", "管理测试2")
    
    scheduler.add_task(task1, "* * * * *")
    scheduler.add_task(task2, "* * * * *")
    
    print(f"📝 已添加 {len(scheduler.get_all_tasks())} 个任务")
    
    # 移除任务
    success = scheduler.remove_task("mgmt_1")
    print(f"🗑️ 移除任务 mgmt_1: {'成功' if success else '失败'}")
    
    # 验证任务数量
    remaining_tasks = scheduler.get_all_tasks()
    print(f"📋 剩余任务数量: {len(remaining_tasks)}")
    
    if len(remaining_tasks) == 1 and remaining_tasks[0]['task_id'] == 'mgmt_2':
        print("✅ 任务管理功能测试成功")
    else:
        print("❌ 任务管理功能测试失败")
    
    # 清理
    import os
    try:
        os.remove("test_management_config.json")
    except:
        pass
    
    print("✅ 任务管理测试完成\n")


def main():
    """主测试函数"""
    print("🚀 XianYu定时任务框架测试套件")
    print("=" * 50)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        # 运行所有测试
        test_cron_parser()
        test_config_persistence()
        test_task_management()
        
        # 询问是否运行实时测试
        print("🤔 是否运行实时调度测试？(这将运行5分钟)")
        response = input("输入 'y' 或 'yes' 继续，其他键跳过: ").lower().strip()
        
        if response in ['y', 'yes']:
            test_basic_functionality()
        else:
            print("⏭️ 跳过实时调度测试")
        
        print("🎉 所有测试完成！")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试文件
        import os
        for test_file in ["test_scheduler_config.json", "test_persistence_config.json", "test_management_config.json"]:
            try:
                os.remove(test_file)
            except:
                pass


if __name__ == "__main__":
    main()
