#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
定时任务调度器模块
基于APScheduler实现的简化定时任务框架，专门为XianYuApis项目设计

Author: 外包程序员
Date: 2025-07-29
"""

import json
import time
import threading
from datetime import datetime
from typing import Dict, Any, Callable, Optional, List
from dataclasses import dataclass, asdict
from enum import Enum
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('XianYuScheduler')


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"      # 等待执行
    RUNNING = "running"      # 正在执行
    COMPLETED = "completed"  # 执行完成
    FAILED = "failed"        # 执行失败
    STOPPED = "stopped"      # 已停止


@dataclass
class TaskResult:
    """任务执行结果"""
    task_id: str
    status: TaskStatus
    start_time: datetime
    end_time: Optional[datetime] = None
    result: Any = None
    error: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        data['status'] = self.status.value
        data['start_time'] = self.start_time.isoformat() if self.start_time else None
        data['end_time'] = self.end_time.isoformat() if self.end_time else None
        return data


class BaseTask:
    """任务基类 - 用户需要继承此类实现具体任务"""
    
    def __init__(self, task_id: str, name: str, description: str = ""):
        self.task_id = task_id
        self.name = name
        self.description = description
        self.created_at = datetime.now()
        
    def execute(self) -> Any:
        """
        任务执行方法 - 子类必须重写此方法
        
        Returns:
            Any: 任务执行结果
            
        Raises:
            NotImplementedError: 子类未实现此方法
        """
        raise NotImplementedError("子类必须实现execute方法")
    
    def on_success(self, result: Any) -> None:
        """任务成功回调 - 子类可选择重写"""
        logger.info(f"任务 {self.task_id} 执行成功: {result}")
    
    def on_error(self, error: Exception) -> None:
        """任务失败回调 - 子类可选择重写"""
        logger.error(f"任务 {self.task_id} 执行失败: {error}")


class CronParser:
    """简化的Cron表达式解析器"""
    
    @staticmethod
    def parse_cron(cron_expr: str) -> Dict[str, Any]:
        """
        解析cron表达式 (简化版本，支持基本格式)
        格式: 分 时 日 月 周
        
        Args:
            cron_expr: cron表达式字符串
            
        Returns:
            Dict: 解析后的时间配置
        """
        parts = cron_expr.strip().split()
        if len(parts) != 5:
            raise ValueError("Cron表达式必须包含5个部分: 分 时 日 月 周")
        
        minute, hour, day, month, day_of_week = parts
        
        config = {}
        
        # 解析分钟
        if minute != '*':
            if '/' in minute:
                config['minute'] = f"*/{minute.split('/')[1]}"
            else:
                config['minute'] = int(minute)
        
        # 解析小时
        if hour != '*':
            if '/' in hour:
                config['hour'] = f"*/{hour.split('/')[1]}"
            else:
                config['hour'] = int(hour)
        
        # 解析日期
        if day != '*':
            config['day'] = int(day)
        
        # 解析月份
        if month != '*':
            config['month'] = int(month)
        
        # 解析星期
        if day_of_week != '*':
            config['day_of_week'] = day_of_week
        
        return config


class SimpleScheduler:
    """简化的定时任务调度器"""
    
    def __init__(self, config_file: str = "scheduler_config.json"):
        self.config_file = config_file
        self.tasks: Dict[str, BaseTask] = {}
        self.schedules: Dict[str, Dict[str, Any]] = {}
        self.results: Dict[str, TaskResult] = {}
        self.running = False
        self.scheduler_thread: Optional[threading.Thread] = None
        self._stop_event = threading.Event()
        
        # 加载配置
        self.load_config()
        
    def load_config(self) -> None:
        """从配置文件加载任务配置"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                self.schedules = config.get('schedules', {})
                logger.info(f"已加载 {len(self.schedules)} 个任务配置")
        except FileNotFoundError:
            logger.info("配置文件不存在，将创建新的配置文件")
            self.schedules = {}
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            self.schedules = {}
    
    def save_config(self) -> None:
        """保存任务配置到文件"""
        try:
            config = {
                'schedules': self.schedules,
                'updated_at': datetime.now().isoformat()
            }
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            logger.info("配置文件已保存")
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
    
    def add_task(self, task: BaseTask, cron_expr: str, enabled: bool = True) -> None:
        """
        添加定时任务
        
        Args:
            task: 任务实例
            cron_expr: cron表达式
            enabled: 是否启用
        """
        try:
            # 解析cron表达式
            cron_config = CronParser.parse_cron(cron_expr)
            
            # 存储任务
            self.tasks[task.task_id] = task
            
            # 存储调度配置
            self.schedules[task.task_id] = {
                'name': task.name,
                'description': task.description,
                'cron_expr': cron_expr,
                'cron_config': cron_config,
                'enabled': enabled,
                'created_at': task.created_at.isoformat(),
                'last_run': None,
                'next_run': None
            }
            
            # 保存配置
            self.save_config()
            
            logger.info(f"任务 {task.task_id} 已添加，cron: {cron_expr}")
            
        except Exception as e:
            logger.error(f"添加任务失败: {e}")
            raise
    
    def remove_task(self, task_id: str) -> bool:
        """
        移除任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功移除
        """
        try:
            if task_id in self.tasks:
                del self.tasks[task_id]
            
            if task_id in self.schedules:
                del self.schedules[task_id]
                self.save_config()
                logger.info(f"任务 {task_id} 已移除")
                return True
            else:
                logger.warning(f"任务 {task_id} 不存在")
                return False
                
        except Exception as e:
            logger.error(f"移除任务失败: {e}")
            return False
    
    def get_task_status(self, task_id: str) -> Optional[TaskResult]:
        """获取任务状态"""
        return self.results.get(task_id)
    
    def get_all_tasks(self) -> List[Dict[str, Any]]:
        """获取所有任务信息"""
        tasks_info = []
        for task_id, schedule in self.schedules.items():
            task_info = schedule.copy()
            task_info['task_id'] = task_id
            
            # 添加最近执行结果
            if task_id in self.results:
                task_info['last_result'] = self.results[task_id].to_dict()
            
            tasks_info.append(task_info)
        
        return tasks_info
    
    def start(self) -> None:
        """启动调度器"""
        if self.running:
            logger.warning("调度器已在运行中")
            return
        
        self.running = True
        self._stop_event.clear()
        self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.scheduler_thread.start()
        logger.info("定时任务调度器已启动")
    
    def stop(self) -> None:
        """停止调度器"""
        if not self.running:
            logger.warning("调度器未在运行")
            return
        
        self.running = False
        self._stop_event.set()
        
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5)
        
        logger.info("定时任务调度器已停止")
    
    def _run_scheduler(self) -> None:
        """调度器主循环"""
        logger.info("调度器主循环开始")
        
        while self.running and not self._stop_event.is_set():
            try:
                current_time = datetime.now()
                
                # 检查每个任务是否需要执行
                for task_id, schedule in self.schedules.items():
                    if not schedule.get('enabled', True):
                        continue
                    
                    if self._should_run_task(task_id, current_time):
                        self._execute_task(task_id)
                
                # 等待1分钟后再次检查
                self._stop_event.wait(60)
                
            except Exception as e:
                logger.error(f"调度器运行错误: {e}")
                self._stop_event.wait(60)
        
        logger.info("调度器主循环结束")
    
    def _should_run_task(self, task_id: str, current_time: datetime) -> bool:
        """检查任务是否应该执行"""
        schedule = self.schedules[task_id]
        cron_config = schedule['cron_config']
        
        # 简化的时间匹配逻辑
        if 'minute' in cron_config:
            if isinstance(cron_config['minute'], int):
                if current_time.minute != cron_config['minute']:
                    return False
        
        if 'hour' in cron_config:
            if isinstance(cron_config['hour'], int):
                if current_time.hour != cron_config['hour']:
                    return False
        
        if 'day' in cron_config:
            if current_time.day != cron_config['day']:
                return False
        
        if 'month' in cron_config:
            if current_time.month != cron_config['month']:
                return False
        
        # 检查是否已经在这一分钟内执行过
        last_run = schedule.get('last_run')
        if last_run:
            last_run_time = datetime.fromisoformat(last_run)
            if (current_time - last_run_time).total_seconds() < 60:
                return False
        
        return True
    
    def _execute_task(self, task_id: str) -> None:
        """执行任务"""
        if task_id not in self.tasks:
            logger.error(f"任务 {task_id} 不存在")
            return
        
        task = self.tasks[task_id]
        start_time = datetime.now()
        
        # 创建任务结果记录
        result = TaskResult(
            task_id=task_id,
            status=TaskStatus.RUNNING,
            start_time=start_time
        )
        self.results[task_id] = result
        
        # 更新最后运行时间
        self.schedules[task_id]['last_run'] = start_time.isoformat()
        
        logger.info(f"开始执行任务: {task_id}")
        
        try:
            # 执行任务
            task_result = task.execute()
            
            # 更新结果
            result.status = TaskStatus.COMPLETED
            result.end_time = datetime.now()
            result.result = task_result
            
            # 调用成功回调
            task.on_success(task_result)
            
            logger.info(f"任务 {task_id} 执行成功")
            
        except Exception as e:
            # 更新结果
            result.status = TaskStatus.FAILED
            result.end_time = datetime.now()
            result.error = str(e)
            
            # 调用失败回调
            task.on_error(e)
            
            logger.error(f"任务 {task_id} 执行失败: {e}")
        
        # 保存配置（更新最后运行时间）
        self.save_config()


if __name__ == "__main__":
    # 示例用法
    print("XianYu定时任务调度器")
    print("请参考文档创建具体的任务类")
