import asyncio
from pyppeteer import launch
import os
import sys
import subprocess
import winreg  # 仅 Windows 使用

def get_chrome_path():
    # 优先尝试专用库
    try:
        from browser_path import get_browser_path
        chrome_path = get_browser_path("chrome")
        if chrome_path and os.path.exists(chrome_path):
            return chrome_path
    except:
        pass

    # 平台特定查找
    if sys.platform == 'win32':
        # Windows 注册表查找
        try:
            with winreg.OpenKey(
                    winreg.HKEY_LOCAL_MACHINE,
                    r"SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\chrome.exe"
            ) as key:
                path = winreg.QueryValue(key, None)
                if os.path.exists(path):
                    return path
        except:
            pass

        # 常见 Windows 路径
        paths = [
            os.environ.get('PROGRAMFILES', 'C:\\Program Files') + '\\Google\\Chrome\\Application\\chrome.exe',
            os.environ.get('PROGRAMFILES(X86)',
                           'C:\\Program Files (x86)') + '\\Google\\Chrome\\Application\\chrome.exe',
            os.environ.get('LOCALAPPDATA', '') + '\\Google\\Chrome\\Application\\chrome.exe',
        ]

    elif sys.platform == 'darwin':
        # macOS 路径
        paths = [
            '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
            os.path.expanduser('~/Applications/Google Chrome.app/Contents/MacOS/Google Chrome'),
        ]

    else:
        # Linux 和其他系统的路径
        paths = [
            '/usr/bin/google-chrome',
            '/usr/bin/google-chrome-stable',
            '/usr/bin/chromium',
            '/usr/bin/chromium-browser',
            '/snap/bin/chromium',
        ]

    # 检测所有路径
    for path in paths:
        if os.path.exists(path):
            return path

    # 使用系统 PATH 查找
    try:
        chrome_path = subprocess.check_output(
            ['which', 'google-chrome'],
            stderr=subprocess.DEVNULL,
            text=True
        ).strip()
        if chrome_path:
            return chrome_path
    except:
        pass

    # 最后尝试 Pyppeteer 的内部方法
    try:
        from pyppeteer import chromium_downloader
        return chromium_downloader.chrome_excutable()
    except ImportError:
        pass

    # 所有方法都失败
    raise EnvironmentError("无法找到系统安装的 Chrome 浏览器路径")


async def main():
    # 启动浏览器
    browser = await launch(
        headless=True,
        executablePath='C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe'

    )
    # 创建新页面
    page = await browser.newPage()
    # 导航到网址
    await page.goto('https://www.goofish.com/')
    # 获取页面标题
    print(await page.title())
    # 关闭浏览器
    await browser.close()


if __name__ == "__main__":
    asyncio.run(main())