import datetime
import os
import platform
import subprocess
import asyncio
from pathlib import Path
from typing import Optional, List

# 导入pyppeteer相关模块
try:
    from pyppeteer import launch
    PYPPETEER_AVAILABLE = True
except ImportError:
    PYPPETEER_AVAILABLE = False
    print("⚠️  Pyppeteer未安装，请运行: pip install pyppeteer")

# 尝试导入pyppeteer_stealth
try:
    from pyppeteer_stealth import stealth
    STEALTH_AVAILABLE = True
    print("✅ pyppeteer_stealth已加载")
except ImportError:
    STEALTH_AVAILABLE = False
    print("❌ pyppeteer_stealth未安装，请运行: pip install pyppeteer-stealth")


def find_chrome_path() -> Optional[str]:
    """
    跨平台查找Chrome浏览器可执行文件路径

    支持的系统:
    - Windows: 查找Chrome.exe
    - macOS: 查找Google Chrome.app
    - Linux: 查找google-chrome或chromium-browser

    Returns:
        str: Chrome可执行文件的完整路径，如果未找到则返回None
    """
    system = platform.system().lower()

    if system == "windows":
        return _find_chrome_windows()
    elif system == "darwin":  # macOS
        return _find_chrome_macos()
    elif system == "linux":
        return _find_chrome_linux()
    else:
        print(f"不支持的操作系统: {system}")
        return None


def _find_chrome_windows() -> Optional[str]:
    """在Windows系统中查找Chrome路径"""
    # Windows常见的Chrome安装路径
    possible_paths = [
        # 用户级安装
        os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe"),
        # 系统级安装
        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
        # 便携版可能的位置
        r"C:\Users\<USER>\Desktop\Google Chrome\chrome.exe",
    ]

    # 检查常见路径
    for path in possible_paths:
        if os.path.isfile(path):
            return path

    # 通过注册表查找
    try:
        import winreg
        # 查找默认浏览器注册表项
        with winreg.OpenKey(winreg.HKEY_CURRENT_USER,
                           r"Software\Microsoft\Windows\Shell\Associations\UrlAssociations\http\UserChoice") as key:
            prog_id = winreg.QueryValueEx(key, "ProgId")[0]

        if "chrome" in prog_id.lower():
            with winreg.OpenKey(winreg.HKEY_CLASSES_ROOT,
                               f"{prog_id}\\shell\\open\\command") as key:
                command = winreg.QueryValueEx(key, "")[0]
                # 提取可执行文件路径
                chrome_path = command.split('"')[1] if '"' in command else command.split()[0]
                if os.path.isfile(chrome_path):
                    return chrome_path
    except (ImportError, OSError, IndexError):
        pass

    # 通过where命令查找
    try:
        result = subprocess.run(['where', 'chrome'],
                              capture_output=True, text=True, shell=True)
        if result.returncode == 0:
            chrome_path = result.stdout.strip().split('\n')[0]
            if os.path.isfile(chrome_path):
                return chrome_path
    except (subprocess.SubprocessError, FileNotFoundError):
        pass

    return None


def _find_chrome_macos() -> Optional[str]:
    """在macOS系统中查找Chrome路径"""
    # macOS常见的Chrome安装路径
    possible_paths = [
        "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
        "/Applications/Google Chrome Canary.app/Contents/MacOS/Google Chrome Canary",
        "/Applications/Chromium.app/Contents/MacOS/Chromium",
        # 用户Applications目录
        os.path.expanduser("~/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"),
    ]

    # 检查常见路径
    for path in possible_paths:
        if os.path.isfile(path):
            return path

    # 使用mdfind命令查找
    try:
        result = subprocess.run(['mdfind', 'kMDItemCFBundleIdentifier == "com.google.Chrome"'],
                              capture_output=True, text=True)
        if result.returncode == 0:
            app_paths = result.stdout.strip().split('\n')
            for app_path in app_paths:
                if app_path and os.path.isdir(app_path):
                    chrome_executable = os.path.join(app_path, "Contents/MacOS/Google Chrome")
                    if os.path.isfile(chrome_executable):
                        return chrome_executable
    except (subprocess.SubprocessError, FileNotFoundError):
        pass

    # 使用which命令查找
    try:
        result = subprocess.run(['which', 'google-chrome'],
                              capture_output=True, text=True)
        if result.returncode == 0:
            chrome_path = result.stdout.strip()
            if os.path.isfile(chrome_path):
                return chrome_path
    except (subprocess.SubprocessError, FileNotFoundError):
        pass

    return None


def _find_chrome_linux() -> Optional[str]:
    """在Linux系统中查找Chrome路径"""
    # Linux常见的Chrome安装路径
    possible_paths = [
        "/usr/bin/google-chrome",
        "/usr/bin/google-chrome-stable",
        "/usr/bin/google-chrome-beta",
        "/usr/bin/google-chrome-unstable",
        "/usr/bin/chromium-browser",
        "/usr/bin/chromium",
        "/snap/bin/chromium",
        "/opt/google/chrome/chrome",
        "/opt/google/chrome/google-chrome",
    ]

    # 检查常见路径
    for path in possible_paths:
        if os.path.isfile(path):
            return path

    # 使用which命令查找
    chrome_commands = [
        "google-chrome",
        "google-chrome-stable",
        "google-chrome-beta",
        "google-chrome-unstable",
        "chromium-browser",
        "chromium"
    ]

    for cmd in chrome_commands:
        try:
            result = subprocess.run(['which', cmd],
                                  capture_output=True, text=True)
            if result.returncode == 0:
                chrome_path = result.stdout.strip()
                if os.path.isfile(chrome_path):
                    return chrome_path
        except (subprocess.SubprocessError, FileNotFoundError):
            continue

    # 检查flatpak安装的Chrome
    try:
        result = subprocess.run(['flatpak', 'list', '--app'],
                              capture_output=True, text=True)
        if result.returncode == 0:
            if 'com.google.Chrome' in result.stdout:
                return 'flatpak run com.google.Chrome'
    except (subprocess.SubprocessError, FileNotFoundError):
        pass

    return None



async def launch_pyppeteer_with_chrome(chrome_path: str, headless: bool = True) -> Optional[object]:
    """
    使用指定的Chrome路径启动Pyppeteer浏览器

    Args:
        chrome_path: Chrome可执行文件路径
        headless: 是否以无头模式运行

    Returns:
        Browser对象，如果启动失败则返回None
    """
    if not PYPPETEER_AVAILABLE:
        print("❌ Pyppeteer未安装，无法启动浏览器")
        return None

    if not STEALTH_AVAILABLE:
        print("❌ pyppeteer-stealth未安装，无法启动反检测浏览器")
        return None

    if not chrome_path or not os.path.exists(chrome_path):
        print("❌ Chrome路径无效")
        return None

    try:
        print(f"🚀 正在使用Chrome启动Pyppeteer浏览器...")
        print(f"   Chrome路径: {chrome_path}")
        print(f"   无头模式: {'是' if headless else '否'}")
        print(f"   反检测模式: 是 (pyppeteer-stealth)")

        # 反检测启动参数
        args = [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--no-first-run',
            '--disable-blink-features=AutomationControlled',  # 关键反检测参数
            '--disable-infobars',                             # 禁用信息栏
            '--disable-extensions',                           # 禁用扩展
            '--no-default-browser-check',                     # 不检查默认浏览器
            '--disable-default-apps',                         # 禁用默认应用
        ]

        # 启动浏览器（忽略默认的自动化参数）
        browser = await launch({
            'executablePath': chrome_path,
            'headless': headless,
            'args': args,
            'ignoreHTTPSErrors': True,
            'autoClose': False,
            'ignoreDefaultArgs': [
                '--enable-automation',      # 关键：忽略自动化标识参数
                '--enable-blink-features=AutomationControlled'  # 忽略自动化控制特性
            ]
        })

        print("✅ Pyppeteer浏览器启动成功！")
        return browser

    except Exception as e:
        print(f"❌ 启动Pyppeteer浏览器失败: {str(e)}")
        return None

# def log_request(request):
#     target_url  = "https://h5api.m.goofish.com/h5/mtop.taobao.idlemtopsearch.pc.search.shade/1.0"
#     if target_url  in request.url:  # 过滤特定请求
#         print(f"[Request] {request.method} {request.url}")
#         # 查看请求头
#         print(f"Headers: {request.headers}")
#
#
# def log_response(response):
#     if response.status == 200 and 'json' in response.headers.get('content-type', ''):
#         print(f"[Response] {response.status} {response.url}")



async def demo_pyppeteer_usage(browser):
    """
    演示Pyppeteer的基本使用（使用pyppeteer-stealth）

    Args:
        browser: Pyppeteer浏览器对象
    """
    try:
        print("\n🌐 开始演示Pyppeteer功能...")

        # 创建新页面
        page = await browser.newPage()
        print("✅ 创建新页面成功")

        # 应用pyppeteer-stealth反检测
        await stealth(page)
        print("✅ pyppeteer-stealth反检测已应用")

        # 设置视口大小
        await page.setViewport({'width': 1280, 'height': 720})


        try:
            await page.goto('https://www.goofish.com/', {'waitUntil': 'networkidle0', 'timeout': 30000})
            print("✅ Bot检测网站加载完成")

            # 等待页面完全加载和检测完成
            await asyncio.sleep(3)

        except Exception as e:
            print(f"❌ 演示过程中出错: {str(e)}")
            exit()

        # 获取指定URL的Cookie
        cookies = await page.cookies('https://h5api.m.goofish.com/h5/mtop.taobao.idlemtopsearch.pc.search.shade/1.0')

        # 打印带详细信息的Cookie
        print("Cookie详情:")
        print("=" * 80)
        for cookie in cookies:
            # 格式化到期时间
            if 'expires' in cookie and cookie['expires'] > 0:
                # 转换为人类可读的时间格式
                expires_dt = datetime.datetime.fromtimestamp(cookie['expires'])
                expires_str = expires_dt.strftime('%Y-%m-%d %H:%M:%S')
                remaining = (expires_dt - datetime.datetime.now()).days
                expires_info = f"{expires_str} (剩余{remaining}天)"
            else:
                expires_info = "会话结束即过期"

            # 格式化其他属性
            same_site = cookie.get('sameSite', '未指定')
            http_only = '是' if cookie.get('httpOnly', False) else '否'
            secure = '是' if cookie.get('secure', False) else '否'

            # 打印详细信息
            print(f"🍪 名称: {cookie['name']}")
            print(f"  值: {cookie['value']}")
            print(f"  作用域: {cookie['domain']}{cookie['path']}")
            print(f"  有效期: {expires_info}")
            print(f"  同站策略: {same_site}")
            print(f"  HTTP Only: {http_only}")
            print(f"  安全传输: {secure}")
            print(f"  大小: {cookie.get('size', '未知')} bytes")
            print("-" * 60)
        # 获取页面标题
        title = await page.title()
        print(f"📄 页面标题: {title}")
        # 在浏览器环境中发送请求
        result = await page.evaluate('''async () => {
            const response = await fetch('https://h5api.m.goofish.com/h5/mtop.taobao.idlemtopsearch.pc.search.shade/1.0', {
                method: 'GET',
            });
            return await response.json();
        }''')
        print(result)


    except Exception as e:
        print(f"❌ 演示过程中出错: {str(e)}")


async def main_async():
    """异步主函数 - 查找Chrome并启动Pyppeteer"""
    print("=== Chrome路径查找与Pyppeteer启动 ===\n")
    print(f"当前操作系统: {platform.system()}")

    # 查找Chrome路径
    print("\n🔍 正在查找Chrome浏览器...")
    chrome_path = find_chrome_path()

    if not chrome_path:
        print("❌ 未找到Chrome浏览器")
        print("请确保已安装Google Chrome或Chromium浏览器")
        return

    print(f"✅ 找到Chrome路径: {chrome_path}")

    # 验证Chrome路径
    if chrome_path.startswith('flatpak'):
        print("📦 检测到Flatpak安装的Chrome")
    elif os.path.isfile(chrome_path) and os.access(chrome_path, os.X_OK):
        print("✅ Chrome可执行文件验证通过")
    elif os.path.isfile(chrome_path):
        print("⚠️  Chrome文件存在但可能无执行权限")
    else:
        print("⚠️  Chrome路径可能不是文件")

    # 启动Pyppeteer（使用pyppeteer-stealth）
    browser = await launch_pyppeteer_with_chrome(chrome_path, headless=False)

    if browser:
        try:
            # 演示基本功能（使用pyppeteer-stealth）
            await demo_pyppeteer_usage(browser)

            print("\n⏳ 浏览器将在5秒后关闭...")
            await asyncio.sleep(5)

        finally:
            # 确保浏览器被关闭
            # await browser.close()
            print("🔒 浏览器已关闭")

    print("\n✅ 程序执行完成")


def main():
    """同步主函数 - 运行异步任务"""
    if not PYPPETEER_AVAILABLE:
        print("❌ 请先安装Pyppeteer: pip install pyppeteer")
        return

    # 运行异步主函数
    asyncio.run(main_async())



if __name__ == "__main__":
    # 运行主程序 - 查找Chrome并启动Pyppeteer
    main()
