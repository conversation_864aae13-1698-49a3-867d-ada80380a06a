#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
示例任务模块
展示如何创建与XianyuApis集成的定时任务

Author: 外包程序员
Date: 2025-07-29
"""

import time
from datetime import datetime
from typing import Any, Dict
from scheduler import BaseTask, SimpleScheduler
from XianyuApis import XianyuApis
from utils.xianyu_utils import trans_cookies, generate_device_id


class XianyuTokenTask(BaseTask):
    """闲鱼Token获取任务示例"""
    
    def __init__(self, task_id: str, cookies_str: str):
        super().__init__(
            task_id=task_id,
            name="闲鱼Token获取任务",
            description="定时获取闲鱼访问Token"
        )
        self.cookies_str = cookies_str
        self.xianyu_api = XianyuApis()
    
    def execute(self) -> Dict[str, Any]:
        """执行Token获取任务"""
        try:
            # 转换cookies
            cookies = trans_cookies(self.cookies_str)
            
            # 生成设备ID
            user_id = cookies.get('unb', 'default_user')
            device_id = generate_device_id(user_id)
            
            # 获取token
            result = self.xianyu_api.get_token(cookies, device_id)
            
            # 返回结果
            return {
                'success': True,
                'token_data': result,
                'device_id': device_id,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def on_success(self, result: Any) -> None:
        """成功回调"""
        if result.get('success'):
            print(f"✅ Token获取成功: {self.task_id}")
            # 这里可以保存token到文件或数据库
            token_data = result.get('token_data', {})
            if 'data' in token_data and 'accessToken' in token_data['data']:
                print(f"   AccessToken: {token_data['data']['accessToken'][:20]}...")
        else:
            print(f"❌ Token获取失败: {result.get('error')}")
    
    def on_error(self, error: Exception) -> None:
        """失败回调"""
        print(f"❌ 任务执行异常: {self.task_id} - {error}")


class HealthCheckTask(BaseTask):
    """系统健康检查任务"""
    
    def __init__(self, task_id: str):
        super().__init__(
            task_id=task_id,
            name="系统健康检查",
            description="定时检查系统状态"
        )
    
    def execute(self) -> Dict[str, Any]:
        """执行健康检查"""
        import psutil
        import os
        
        try:
            # 获取系统信息
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # 检查进程状态
            current_process = psutil.Process(os.getpid())
            process_memory = current_process.memory_info()
            
            return {
                'success': True,
                'system_info': {
                    'cpu_percent': cpu_percent,
                    'memory_percent': memory.percent,
                    'memory_available_gb': round(memory.available / (1024**3), 2),
                    'disk_percent': disk.percent,
                    'disk_free_gb': round(disk.free / (1024**3), 2)
                },
                'process_info': {
                    'memory_mb': round(process_memory.rss / (1024**2), 2),
                    'pid': os.getpid()
                },
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def on_success(self, result: Any) -> None:
        """成功回调"""
        if result.get('success'):
            sys_info = result.get('system_info', {})
            print(f"💚 系统状态正常: CPU {sys_info.get('cpu_percent', 0):.1f}%, "
                  f"内存 {sys_info.get('memory_percent', 0):.1f}%")
        else:
            print(f"❌ 健康检查失败: {result.get('error')}")


class LogCleanupTask(BaseTask):
    """日志清理任务"""
    
    def __init__(self, task_id: str, log_dir: str = "logs", max_days: int = 7):
        super().__init__(
            task_id=task_id,
            name="日志清理任务",
            description=f"清理{max_days}天前的日志文件"
        )
        self.log_dir = log_dir
        self.max_days = max_days
    
    def execute(self) -> Dict[str, Any]:
        """执行日志清理"""
        import os
        import glob
        from datetime import datetime, timedelta
        
        try:
            if not os.path.exists(self.log_dir):
                return {
                    'success': True,
                    'message': f"日志目录 {self.log_dir} 不存在",
                    'cleaned_files': 0,
                    'timestamp': datetime.now().isoformat()
                }
            
            # 计算截止时间
            cutoff_time = datetime.now() - timedelta(days=self.max_days)
            
            # 查找日志文件
            log_files = glob.glob(os.path.join(self.log_dir, "*.log"))
            cleaned_files = 0
            
            for log_file in log_files:
                try:
                    # 获取文件修改时间
                    file_time = datetime.fromtimestamp(os.path.getmtime(log_file))
                    
                    if file_time < cutoff_time:
                        os.remove(log_file)
                        cleaned_files += 1
                        print(f"   删除日志文件: {log_file}")
                        
                except Exception as e:
                    print(f"   删除文件失败 {log_file}: {e}")
            
            return {
                'success': True,
                'cleaned_files': cleaned_files,
                'log_dir': self.log_dir,
                'max_days': self.max_days,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def on_success(self, result: Any) -> None:
        """成功回调"""
        if result.get('success'):
            cleaned = result.get('cleaned_files', 0)
            if cleaned > 0:
                print(f"🧹 日志清理完成: 删除了 {cleaned} 个文件")
            else:
                print("🧹 日志清理完成: 无需清理的文件")


def create_example_scheduler():
    """创建示例调度器配置"""
    
    # 创建调度器
    scheduler = SimpleScheduler("example_scheduler_config.json")
    
    # 示例cookies（需要替换为真实的）
    cookies_str = "your_cookies_here"  # 请替换为真实的cookies
    
    # 添加任务示例
    
    # 1. 每小时获取一次Token
    if cookies_str != "your_cookies_here":  # 只有在提供真实cookies时才添加
        token_task = XianyuTokenTask("token_hourly", cookies_str)
        scheduler.add_task(token_task, "0 * * * *")  # 每小时的0分执行
    
    # 2. 每5分钟进行健康检查
    health_task = HealthCheckTask("health_check")
    scheduler.add_task(health_task, "*/5 * * * *")  # 每5分钟执行
    
    # 3. 每天凌晨2点清理日志
    cleanup_task = LogCleanupTask("log_cleanup", "logs", 7)
    scheduler.add_task(cleanup_task, "0 2 * * *")  # 每天凌晨2点执行
    
    return scheduler


def main():
    """主函数 - 演示调度器使用"""
    print("🚀 XianYu定时任务调度器示例")
    print("=" * 50)
    
    # 创建调度器
    scheduler = create_example_scheduler()
    
    # 显示所有任务
    tasks = scheduler.get_all_tasks()
    print(f"📋 已配置 {len(tasks)} 个任务:")
    for task in tasks:
        status = "✅ 启用" if task.get('enabled') else "❌ 禁用"
        print(f"   - {task['name']} ({task['task_id']}) - {task['cron_expr']} - {status}")
    
    print("\n🎯 启动调度器...")
    scheduler.start()
    
    try:
        print("⏰ 调度器运行中，按 Ctrl+C 停止")
        print("📊 任务执行日志:")
        
        # 保持运行
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n🛑 收到停止信号")
        scheduler.stop()
        print("✅ 调度器已停止")


if __name__ == "__main__":
    main()
