# 常用模式和最佳实践

- 在1.py中实现了跨平台Chrome路径查找和Pyppeteer集成功能：包含find_chrome_path()跨平台查找函数、launch_pyppeteer_with_chrome()启动函数、demo_pyppeteer_usage()演示函数。支持Windows/macOS/Linux系统，可自动检测Chrome安装路径并用于Pyppeteer自动化。
- 在1.py中增强了Pyppeteer反检测功能：包含get_stealth_args()获取反检测启动参数、apply_stealth_settings()应用页面级反检测设置、支持pyppeteer_stealth插件集成。可有效隐藏webdriver属性、模拟Chrome对象、修改navigator属性等，降低被网站检测为机器人的风险。
- 1.py已重构为使用pyppeteer-stealth专业反检测方案：移除了手动反检测代码，简化为直接使用pyppeteer_stealth.stealth()函数。现在代码更简洁、更可靠，依赖专业的反检测库来隐藏自动化痕迹。需要安装pyppeteer-stealth依赖。
- 1.py增加了请求监听功能：专门监听https://h5api.m.goofish.com/h5/mtop.taobao.idlemtopsearch.pc.search.shade/1.0这个闲鱼搜索API，能够捕获并打印完整的请求头、Cookie信息和POST数据。程序会访问闲鱼网站并尝试触发搜索来产生目标API请求。
